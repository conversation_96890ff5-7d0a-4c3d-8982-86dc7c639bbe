import { DeliverableItem } from "~/shared/types/Deliverable";
import { Target } from "~/shared/types/Target";

export type CreateTargetDrawerProps = {
  proposalId: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccessSubmit: (response: any) => void;
  deliverable?: DeliverableItem;
};

export type FormValues = {
  definition: string;
  calculationMethod: string;
  weight: string;
  scope: string;
};

export type CreateTargetBody = {
  targets: Target[];
};
