import React from 'react';
import { Container } from '@ghq-abi/design-system-v2';

import { TargetCard } from '~/shared/components/TargetCard';
import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { TabEmptyState } from '../TabEmptyState';

interface FeedbackStepProps {
  targets: Target[];
  proposalStatus?: ProposalStatusEnum;
}

export function FeedbackStep({ targets, proposalStatus }: FeedbackStepProps) {
  const feedbackTargets = targets.filter(target =>
    target.targetTypes?.some(
      targetType => targetType.type === TargetTypeEnum.FEEDBACK,
    ),
  );

  if (
    proposalStatus === ProposalStatusEnum.IN_PROGRESS_PROPOSAL ||
    feedbackTargets.length === 0
  ) {
    return (
      <TabEmptyState
        title="Nothing to see here yet"
        description="This page will be populated when the employee completes their feedback list"
      />
    );
  }

  return (
    <>
      <Container className="flex flex-col gap-4">
        {feedbackTargets.map(target => (
          <TargetCard
            key={target.uid}
            data={target}
            proposalStatus={proposalStatus}
            currentTargetType={TargetTypeEnum.FEEDBACK}
          />
        ))}
      </Container>
    </>
  );
}
