import React, { useMemo, useState } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { useTranslate } from '@tolgee/react';
import { useCatalog } from '~/entities/Home/hooks/useCatalog';
import { useCatalogFilter } from '~/entities/Home/hooks/useCatalogFilter';
import { CatalogCard } from '~/entities/Home/components';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { TargetsList } from './TargetsList';
import { Trash } from 'react-bootstrap-icons';
import { Target } from '~/shared/types/Target';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';
import { ChildCard, TargetCard } from '~/shared/components';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { DroppableTarget } from './DroppableTarget';
import { CatalogListProposal } from '../CatalogListProposal';
import { Button, FooterActionBar } from '@ghq-abi/design-system-v2';
import { CreateTargetDrawer } from './CreateTargetDrawer';
import { ActionModal } from '~/shared/components/ActionModal';
import { useActionModal } from '~/shared/components/ActionModal/useActionModal';
import { useMutation } from '@tanstack/react-query';
import proposalService from '~/shared/services/proposal';
import { Proposal } from '~/shared/types/Proposal';
import { CreateTargetBody } from '../../types';

interface ProposalDragProps {
  proposalStatus?: ProposalStatusEnum;
  proposalUid: string;
  targets: Target[];
}

export function ProposalDrag({ proposalStatus, proposalUid, targets }: ProposalDragProps) {
  const { t } = useTranslate();
  const actionModal = useActionModal();
  const [draggedItem, setDraggedItem] = useState<DeliverableItem | Target | null>(null);
  const [selectedTargets, setSelectedTargets] = useState<Target[]>(targets || []);
  const [isDraggingNewItem, setIsDraggingNewItem] = useState(false);
  const [isOpenDrawer, setIsOpenDrawer] = useState<boolean>(false);
  const [drawerDeliverable, setDrawerDeliverable] = useState<DeliverableItem>();

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3,
      },
    }),
  );

  const { isLoading, mutate: mutateFeedback } = useMutation({
    mutationFn: () => {
      return proposalService.changeProposalStatus(proposalUid, ProposalStatusEnum.IN_PROGRESS_FEEDBACK);
    },
    onSuccess: (response: Proposal) => {
      console.log('Proposal sent to feedback:', response);
    },
    onError: error => {
      console.error(error);
    }
  });

  const { isLoading: isLoadingDelete, mutate: mutateDelete } = useMutation({
    mutationFn: (targets: Target[]) => {
      return proposalService.deleteTargets(proposalUid, targets.map(t => t.uid || ''));
    },
    onSuccess: (proposal: Proposal) => {
      setSelectedTargets(proposal.targets || []);
      actionModal.closeModal();
    },
    onError: error => {
      console.error(error);
    }
  });

  const getChildForBodyRequest = (child: Target[]) => {
    return child.map(c => ({
      weight: c.weight,
      scope: c.scope,
      uidDeliverable: c.deliverable?.uid || ''
    }));
  }

  const { isLoading: isLoadingMergeTargets, mutate: mutateMergeTargets } = useMutation({
    mutationFn: (values: { firstTarget: Target; secondTarget: Target }) => {
      const firstTarget = values.firstTarget as Target;
      const secondTarget = values.secondTarget as Target;

      const treatedChildren = getChildForBodyRequest(firstTarget.children || []);
      treatedChildren.push({
        weight: secondTarget.weight,
        scope: secondTarget.scope,
        uidDeliverable: secondTarget.children?.[0].deliverable?.uid || ''
      });

      const data: CreateTargetBody = {
        targets: [
          {
            weight: (firstTarget.weight || 0) + (secondTarget.weight || 0),
            targetType: TargetTypeEnum.PROPOSAL,
            children: treatedChildren,
          }
        ]
      }

      return proposalService.createTarget(proposalUid, data);
    },
    onSuccess: (proposal: Proposal) => {
      setSelectedTargets(proposal.targets || []);
    },
    onError: error => {
      console.error(error);
    }
  });

    const catalogFilterHook = useCatalogFilter();
    const { filters } = catalogFilterHook;
  
    const {
      data: deliverables,
      isSearchLoading,
      isInitialLoading,
      isError,
    } = useCatalog(1, 100, undefined, filters);

  const availableDeliverables = useMemo(() => {
    const selectedUids = new Set(selectedTargets.flatMap(d => {
      if (d.children && d.children.length > 0) {
        return d.children.map(child => child.deliverable?.uid).filter(Boolean);
      } else {
        return d.deliverable?.uid ? [d.deliverable.uid] : [];
      }
    }));

    const deliverableItems = deliverables?.data || [];
    return deliverableItems.filter(
      d =>
        !selectedUids.has(d.uid),
    );
  }, [deliverables, selectedTargets]);

  const handleDragStart = (event: DragStartEvent) => {
    const item = event.active.data.current?.data;

    if (item) {
      setDraggedItem(item);
      const isFromSideList = availableDeliverables.some(
        d => d.uid === item.uid,
      );
      setIsDraggingNewItem(isFromSideList);
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && over.id === 'selection-area') {
      const deliverable = active.data.current?.data;
      if (
        deliverable &&
        !selectedTargets.find(d => d.uid === deliverable.uid)
      ) {
        // For testing, because we will need to open the drawer and get the target
        // object after saving it with weight and scope
        setDrawerDeliverable(deliverable);
        setIsOpenDrawer(true);
        // addTargetChild(deliverable);
      }
    } else if (over && over.id.toString().includes('target')) {
      const firstTarget = over.data.current?.target as Target;
      const secondTarget = active.data.current?.data as Target;

      if (
        firstTarget &&
        secondTarget &&
        firstTarget.uid !== secondTarget.uid 
      ) {
        actionModal.openModal({
        title: t('common_merge_targets'),
          message: t('common_do_you_really_want_to_merge_the_targets'),
          actions: [
            {
              label: t('common_yes'),
              onClick: () => {},
          },
          {
            label: t('common_no'),
            onClick: actionModal.closeModal,
            variant: 'secondary',
          },
        ],
        onConfirm: async () => {
          const values = { firstTarget, secondTarget };
          await mutateMergeTargets(values);
          actionModal.closeModal();
        },
      });

        // addParentTarget(firstTarget, secondTarget);
      }
    }

    setDraggedItem(null);
    setIsDraggingNewItem(false);
  };

  const onDrawerSuccessSubmit = (proposal: Proposal) => {
    setSelectedTargets(proposal.targets || []);
  };

  const handleSubmit = () => {
    mutateFeedback()
  }

  const handleClearDeliverable = () => {
    actionModal.openModal({
      title: t('common_clear_list'),
      message: t('common_do_you_really_want_to_clear_the_list'),
      actions: [
        {
          label: t('common_yes'),
          onClick: () => {},
        },
        {
          label: t('common_no'),
          onClick: actionModal.closeModal,
          variant: 'secondary',
        },
      ],
      onConfirm: async () => {
        if (isLoadingDelete) return;
        await mutateDelete(selectedTargets);
        
      },
    });
  };

  const handleClearSelection = () => {
    setSelectedTargets([]);
  };

  const handleTargetRemove = (targetId: string) => {
    setSelectedTargets(prev => prev.filter(t => t.uid !== targetId));
  };

  return (
    <>
      <DndContext
        sensors={sensors}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div className="flex h-[calc(100vh-340px)] gap-6">
          <CatalogListProposal
            deliverables={availableDeliverables}
            title={t('common_catalog')}
            isInitialLoading={isInitialLoading}
            isSearchLoading={isSearchLoading}
            isError={isError}
            catalogFilterHook={catalogFilterHook}
          />
          <TargetsList
            selectedData={selectedTargets}
            isDraggingNewItem={isDraggingNewItem}
            actions={[
              {
                label: t('common_clear_list'),
                onClick: handleClearDeliverable,
                variant: 'secondary',
                iconLeft: <Trash />,
                disabled: selectedTargets.length === 0,
              },
            ]}
          >
            {selectedTargets.map(target => {
              if (target.children && target.children.length > 1) {
                return (
                  <DroppableTarget isDragging={isDraggingNewItem} key={target.uid} target={target}>
                    <TargetCard
                      key={target.uid}
                      data={target}
                      proposalStatus={proposalStatus}
                      hideChildren={target.children.length <= 1}
                      currentTargetType={TargetTypeEnum.PROPOSAL}
                    />
                  </DroppableTarget>
                )
              }
              return (
                <DroppableTarget isDragging={isDraggingNewItem} key={target.uid} target={target}>
                  <ChildCard
                    key={target.uid}
                    target={target}
                    disableDrag={false}
                    // deleteEvent={() => handleTargetRemove(`${target.uid}`)}
                  />
                </DroppableTarget>
              )
            })}
          </TargetsList>
        </div>
        <DragOverlay>
          {(draggedItem as Target)?.deliverable ? <ChildCard target={(draggedItem as Target)} /> : <CatalogCard data={(draggedItem as DeliverableItem)} isDragging />}
        </DragOverlay>
      </DndContext>
      <CreateTargetDrawer
        isOpen={isOpenDrawer}
        onClose={() => setIsOpenDrawer(false)}
        deliverable={drawerDeliverable}
        proposalId={proposalUid}
        onSuccessSubmit={onDrawerSuccessSubmit}
      />
      <ActionModal
          isOpen={actionModal.isOpen}
          openModal={actionModal.openModal}
          closeModal={actionModal.closeModal}
          title={actionModal.title}
          message={actionModal.message}
          actions={[
            {
              label: t('common_yes'),
              onClick: actionModal.handleConfirm,
              variant: 'primary',
            },
            {
              label: t('common_no'),
              onClick: actionModal.closeModal,
              variant: 'secondary',
            },
          ]}
        />
      <FooterActionBar>
        <Button
          variant="secondary"
          border="default"
          className="w-fit"
          onClick={() => window.history.back()}
        >
          Back
        </Button>
        <Button
          id="deliverable-form"
          isLoading={isLoading}
          variant="primary"
          className="w-fit"
          round="md"
          onClick={handleSubmit}
        >
          {t('common_submit')}
        </Button>
      </FooterActionBar>
    </>
  );
}
