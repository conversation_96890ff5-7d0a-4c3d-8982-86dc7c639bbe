export interface ProposalFilterOptions {
  status?: string[];
  zones?: string[];
  businessFunctions?: string[];
  fuzzy_search?: string;
  stlLevel?: string[];
  stagedBusinessFunctions?: string[];
  stagedZones?: string[];
  stagedStlLevel?: string[];
  stagedStatus?: string[];
}

export type ProposalFilterProps = {
  searchQuery: string;
  handleSearchChange: (query: string) => void;
  handleSearchKeyPress: (e: React.KeyboardEvent) => void;
  handleToggle: (key: string, propName: string) => void;
  selectedFunctions: string[];
  selectedStatus: string[];
  selectedZones: string[];
  selectedStlLevel: string[];
  handleSearchSubmit: () => void;
};
