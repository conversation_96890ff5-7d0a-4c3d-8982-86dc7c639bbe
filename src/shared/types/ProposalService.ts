import { FilterParams } from './FilterParams';
import { Proposal } from './Proposal';

export type ProposalFilters = FilterParams & {
  zones?: string[];
  status?: string[];
  stlLevel?: string[];
  businessFunctions?: string[];
};

export type ProposalItemsResponse = {
  data: Proposal[];
  pageNumber: number;
  pageSize: number;
  totalRecords: number;
};

export type ProposalItemResponse = Proposal[];

export interface IProposalService {
  getProposals(filters: ProposalFilters): Promise<ProposalItemsResponse>;
}
