import { useMemo } from 'react';
import { useRouter } from 'next/router';
import { signOut, useSession } from 'next-auth/react';
import { useTranslate } from '@tolgee/react';

import { useAbility } from '~/app/contexts/AbilityContext';
import { useSessionInfo } from '~/app/contexts/SessionInfoContext';
import { useGetProfilePicture } from '~/entities/Employee/hooks/useGetProfilePicture';
import { SignOut } from '~/shared/components/icons/SignOut';

type UseUserDropdownProps = {
  user: any;
};

export const useUserDropdown = ({ user }: UseUserDropdownProps) => {
  const { t } = useTranslate();
  const nextAuthSession = useSession();
  const { sessionInfo } = useSessionInfo();
  const router = useRouter();
  const ability = useAbility();
  const basePath = process.env.NEXT_PUBLIC_BASE_PATH;

  async function handleSignOut() {
    signOut({ redirect: false });
  }

  const items = useMemo(() => {
    const dropdownItems = [
      {
        label: t('common_logout'),
        onSelect: handleSignOut,
        icon: <SignOut />,
      },
    ];

    return dropdownItems;
  }, [ability, t]);

  const northstarUser = JSON.parse(localStorage.getItem('user') ?? '{}');
  const { data } = useGetProfilePicture(
    user.globalId ?? northstarUser.int_employeeglobalid ?? '',
  );
  const imageSrc = data ? URL.createObjectURL(data) : undefined;

  const profileInfo = {
    name: user.name ?? '',
    urlImage: imageSrc,
    zone: user.zone ?? '',
  };

  return {
    items,
    profileInfo,
  };
};
