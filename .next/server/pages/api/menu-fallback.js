"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/menu-fallback";
exports.ids = ["pages/api/menu-fallback"];
exports.modules = {

/***/ "lodash/uniq":
/*!******************************!*\
  !*** external "lodash/uniq" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("lodash/uniq");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmenu-fallback&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fapi%2Fmenu-fallback.ts&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmenu-fallback&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fapi%2Fmenu-fallback.ts&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_menu_fallback_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/pages/api/menu-fallback.ts */ \"(api)/./src/pages/api/menu-fallback.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_menu_fallback_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_menu_fallback_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/menu-fallback\",\n        pathname: \"/api/menu-fallback\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_menu_fallback_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmenu-fallback&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fapi%2Fmenu-fallback.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/menu-fallback.ts":
/*!****************************************!*\
  !*** ./src/pages/api/menu-fallback.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var lodash_uniq__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash/uniq */ \"lodash/uniq\");\n/* harmony import */ var lodash_uniq__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lodash_uniq__WEBPACK_IMPORTED_MODULE_0__);\n\nconst filterMenuItems = (permissions)=>{\n    const allMenuItems = [\n        {\n            placement: \"BODY\",\n            path: \"/\",\n            title: \"common_home\",\n            icon: \"HouseIcon\",\n            key: \"D52D8FEF-C6A1-49AB-96A3-8E69526B77A0\",\n            privilege: null,\n            routeComponents: null\n        },\n        {\n            placement: \"BODY\",\n            path: \"/catchball\",\n            title: \"common_catchball\",\n            icon: \"CatchballIcon\",\n            key: \"187933AE-0BC1-44BC-AAA6-E5D81A5A296F0\",\n            privilege: \"ACCESS_CATCHBALL\",\n            routeComponents: null\n        },\n        {\n            placement: \"BODY\",\n            path: \"/dashboard/cascading-completion\",\n            title: \"Dashboard\",\n            icon: \"ChartIcon\",\n            key: \"40B20924-E19C-4577-B3D3-63719F995458\",\n            privilege: \"ACCESS_TEAM_DASHBOARD\",\n            routeComponents: [\n                {\n                    path: \"/dashboard/cascading-completion\",\n                    title: \"Cascading Completion\",\n                    icon: \"ChartIcon\",\n                    key: \"41F97310-50FB-4F5E-886F-2AB7C3FFEE88\",\n                    privilege: \"ACCESS_TEAM_DASHBOARD\"\n                },\n                {\n                    path: \"/dashboard/appraisal-completion\",\n                    title: \"Appraisal Completion\",\n                    icon: \"ChartIcon\",\n                    key: \"5F43F10D-6524-41DF-B696-93F9BF51240F\",\n                    privilege: \"ACCESS_TEAM_DASHBOARD\"\n                },\n                {\n                    path: \"/dashboard/mid-year-review\",\n                    title: \"Mid Year Review\",\n                    icon: \"ChartIcon\",\n                    key: \"D1BAEEF3-CEC7-4F25-A098-AB9C63DEEC7D\",\n                    privilege: \"MYR_REQUEST_LIST\"\n                }\n            ]\n        },\n        {\n            placement: \"BODY\",\n            path: \"/catalog\",\n            title: \"common_catalog\",\n            icon: \"MenuBookStarIcon\",\n            key: \"C9429C93-0F70-4EBB-9239-A5C21CC4F453\",\n            privilege: \"ACCESS_CATALOG_REQUEST\",\n            routeComponents: [\n                {\n                    path: \"/catalog\",\n                    title: \"kpi_catalog\",\n                    icon: \"MenuBookStarIcon\",\n                    key: \"ACE4B100-D6BC-4DE3-9A65-C2DE1DCBE884\",\n                    privilege: \"ACCESS_CATALOG_REQUEST\"\n                },\n                {\n                    path: \"/catalog/new-kpi-catalog\",\n                    title: \"new_kpi_catalog\",\n                    icon: \"MenuBookStarIcon\",\n                    key: \"E9C2EFE2-C76B-4815-8DCF-82DF8B4DF3BC\",\n                    privilege: \"ACCESS_CATALOG_REQUEST\"\n                },\n                {\n                    path: \"/catalog/requests\",\n                    title: \"common_requests\",\n                    icon: \"MenuBookStarIcon\",\n                    key: \"41D3E016-1775-4DB7-A8AC-EB6419406C8E\",\n                    privilege: \"ACCESS_CATALOG_REQUEST\"\n                }\n            ]\n        },\n        {\n            placement: \"BODY\",\n            path: \"/shared-target\",\n            title: \"menu_shared_target_management\",\n            icon: \"LeftArrowIcon\",\n            key: \"F41DC0F6-67A1-446E-B3B7-3B7F94921869\",\n            privilege: \"SHARED_TARGETS_LIST\",\n            routeComponents: null\n        },\n        {\n            placement: \"BODY\",\n            path: \"/my-targets/current\",\n            title: \"common_my_targets\",\n            icon: \"BullseyeIcon\",\n            key: \"5DFF6F81-1931-4C05-A21B-D0F61A0ACA4D\",\n            privilege: \"ACCESS_TARGETS\",\n            routeComponents: null\n        },\n        {\n            placement: \"BODY\",\n            path: \"/my-team/cascading\",\n            title: \"common_my_team\",\n            icon: \"PeopleIcon\",\n            key: \"6D48D06D-0547-4AE2-9236-24A98961BF8C\",\n            privilege: \"ACCESS_TEAM_TARGETS\",\n            routeComponents: [\n                {\n                    path: \"/my-team/cascading\",\n                    title: \"common_cascading\",\n                    icon: \"PeopleIcon\",\n                    key: \"4BD24695-FA8A-4505-8188-4D898C533257\",\n                    privilege: \"ACCESS_TEAM_TARGETS_CASCADING\"\n                },\n                {\n                    path: \"/my-team/trackmonitoring\",\n                    title: \"common_tracking_monitoring\",\n                    icon: \"PeopleIcon\",\n                    key: \"47250606-2C81-4552-8144-B5B6C4D47512\",\n                    privilege: \"ACCESS_TEAM_TARGETS_CURRENT\"\n                },\n                {\n                    path: \"/my-team/appraisal\",\n                    title: \"common_appraisal\",\n                    icon: \"PeopleIcon\",\n                    key: \"E5E573AA-7834-4EC7-935D-0BB4AE2F8E4F\",\n                    privilege: \"ACCESS_TEAM_TARGETS_CURRENT\"\n                }\n            ]\n        },\n        {\n            placement: \"BODY\",\n            path: \"/my-scope/cascading\",\n            title: \"menu_my_scope\",\n            icon: \"BuildingIcon\",\n            key: \"65602EC2-C49E-4DD6-AD8B-9AC892593458\",\n            privilege: \"ACCESS_MY_SCOPE\",\n            routeComponents: [\n                {\n                    path: \"/my-scope/cascading\",\n                    title: \"common_cascading\",\n                    icon: \"BuildingIcon\",\n                    key: \"2BC73130-EA81-4843-8EC6-A3D67770F6C0\",\n                    privilege: \"ACCESS_MY_SCOPE_CASCADING\"\n                },\n                {\n                    path: \"/my-scope/trackmonitoring\",\n                    title: \"common_tracking_monitoring\",\n                    icon: \"BuildingIcon\",\n                    key: \"BF747752-35E0-4EE1-8E48-72C2F1962FCA\",\n                    privilege: \"ACCESS_MY_SCOPE_CURRENT\"\n                },\n                {\n                    path: \"/my-scope/appraisal\",\n                    title: \"common_appraisal\",\n                    icon: \"BuildingIcon\",\n                    key: \"07220BDF-CC31-4F34-A9EC-1417833FBC1B\",\n                    privilege: \"ACCESS_MY_SCOPE_CURRENT\"\n                }\n            ]\n        },\n        {\n            placement: \"BODY\",\n            path: \"/my-organization\",\n            title: \"menu_my_organization\",\n            icon: \"DiagramIcon\",\n            key: \"0F6391A1-DCE0-406E-9234-BAF9660A4108\",\n            privilege: \"ACCESS_MYORGANIZATION\",\n            routeComponents: null\n        },\n        {\n            placement: \"BODY\",\n            path: \"/rewards/current\",\n            title: \"menu_rewards\",\n            icon: \"MenuMoneyBagIcon\",\n            key: \"568D1A6D-FF90-4692-8FF0-0F276F428CB3\",\n            privilege: \"ACCESS_REWARDS\",\n            routeComponents: [\n                {\n                    path: \"/rewards/current\",\n                    title: \"menu_rewards\",\n                    icon: \"MenuMoneyBagIcon\",\n                    key: \"C680D8A0-75BC-4698-A44A-69B27866CDD1\",\n                    privilege: \"ACCESS_REWARDS\"\n                },\n                {\n                    path: \"/rewards/bonus-calculation\",\n                    title: \"common_bonus_calculation_module\",\n                    icon: \"MenuMoneyBagIcon\",\n                    key: \"B2A11AC5-B0C6-46E1-88BF-7865D368CA59\",\n                    privilege: \"ACCESS_BONUS_CALCULATIONS\"\n                },\n                {\n                    path: \"/rewards/band-multiplier\",\n                    title: \"Band Multiplier\",\n                    icon: \"MenuMoneyBagIcon\",\n                    key: \"C39CB6F5-C02D-409F-88F6-64422537A376\",\n                    privilege: \"ACCESS_BAND_MULTIPLIERS\"\n                },\n                {\n                    path: \"/rewards/individual-incentive\",\n                    title: \"Individual Incentive\",\n                    icon: \"MenuMoneyBagIcon\",\n                    key: \"70002A6B-C7E7-45CE-ACCF-7EDC134228AF\",\n                    privilege: \"ACCESS_INDIVIDUAL_INCENTIVES\"\n                },\n                {\n                    path: \"/rewards/file-group\",\n                    title: \"File Group\",\n                    icon: \"MenuMoneyBagIcon\",\n                    key: \"6A14D8E7-1F9E-4799-8911-97B922035967\",\n                    privilege: \"ACCESS_FILE_GROUPS\"\n                },\n                {\n                    path: \"/rewards/bonus-election-periods\",\n                    title: \"Bonus Election Period\",\n                    icon: \"MenuMoneyBagIcon\",\n                    key: \"17F7C2A2-EDB3-4CD3-BD04-823DD2F01EAB\",\n                    privilege: \"ACCESS_BONUS_ELECTIONS\"\n                }\n            ]\n        },\n        {\n            placement: \"BODY\",\n            path: \"/admin-view/target-document\",\n            title: \"menu_admin_view\",\n            icon: \"PersonGearIcon\",\n            key: \"1FC30308-2431-41DD-87C4-BE99481F00A6\",\n            privilege: \"ACCESS_MGMT\",\n            routeComponents: [\n                {\n                    path: \"/admin-view/admin/employee\",\n                    title: \"menu_mass_creation\",\n                    icon: \"PersonGearIcon\",\n                    key: \"D34D0B5C-38C7-4883-BF0F-0E56342B7784\",\n                    privilege: \"ACCESS_MGMT\"\n                },\n                {\n                    path: \"/admin-view/catalog\",\n                    title: \"menu_catalog_management\",\n                    icon: \"PersonGearIcon\",\n                    key: \"114E5F8B-5728-4466-AF43-B0A6BB0FB47A\",\n                    privilege: \"ACCESS_MGMT\"\n                },\n                {\n                    path: \"/admin-view/target-document\",\n                    title: \"menu_target_document\",\n                    icon: \"PersonGearIcon\",\n                    key: \"92F10C0E-BDFC-4B17-9376-8AE4C363161E\",\n                    privilege: \"ACCESS_MGMT\"\n                },\n                {\n                    path: \"/admin-view/communications\",\n                    title: \"common_communication_module\",\n                    icon: \"PersonGearIcon\",\n                    key: \"F32A8C03-8D62-49F0-BBE1-D6AE0A335A56\",\n                    privilege: \"ACCESS_MGMT\"\n                },\n                {\n                    path: \"/admin-view/helpcenter\",\n                    title: \"menu_faq\",\n                    icon: \"PersonGearIcon\",\n                    key: \"3C8D2585-38E3-4154-B9D6-04689194FE38\",\n                    privilege: \"ACCESS_MGMT\"\n                },\n                {\n                    path: \"/admin-view/entities-targets\",\n                    title: \"menu_entities_targets\",\n                    icon: \"PersonGearIcon\",\n                    key: \"0EE7145D-B260-4DA9-9FCE-ED734DB1CD5F\",\n                    privilege: \"ACCESS_MGMT\"\n                },\n                {\n                    path: \"/admin-view/performance-entity\",\n                    title: \"performance_entity\",\n                    icon: \"PersonGearIcon\",\n                    key: \"6BDD90AD-15CC-420C-817B-506CAAE777C9\",\n                    privilege: \"ACCESS_MGMT\"\n                },\n                {\n                    path: \"/admin-view/sop\",\n                    title: \"common_sop_module\",\n                    icon: \"PersonGearIcon\",\n                    key: \"8E75211D-8C01-4714-986C-7D87EC80DC4E\",\n                    privilege: \"ACCESS_MGMT\"\n                },\n                {\n                    path: \"/admin-view/management/user\",\n                    title: \"common_user_management_module\",\n                    icon: \"PersonGearIcon\",\n                    key: \"F2918F41-62FB-47EB-9D3E-B675ECEBE134\",\n                    privilege: \"ACCESS_MGMT\"\n                },\n                {\n                    path: \"/admin-view/management/supervisory\",\n                    title: \"common_supervisory_module\",\n                    icon: \"PersonGearIcon\",\n                    key: \"7CE4FF2F-6199-477E-9E1A-6144ACB5B37D\",\n                    privilege: \"CASCADING_DEPTH_SCREEN\"\n                }\n            ]\n        },\n        {\n            placement: \"BODY\",\n            path: \"/reporting-module\",\n            title: \"common_reporting_module\",\n            icon: \"ClipboardCheckLargeIcon\",\n            key: \"7D86DE7F-AC31-4076-AEB1-061ECA7BF081\",\n            privilege: \"REPORT_LIST\",\n            routeComponents: null\n        },\n        {\n            placement: \"BODY\",\n            path: \"/download-center\",\n            title: \"common_download_center\",\n            icon: \"DownloadIcon\",\n            key: \"E85C8360-F1ED-41F9-9807-2213097A0DAC\",\n            privilege: \"ACCESS_DOWNLOAD_CENTER\",\n            routeComponents: null\n        },\n        {\n            placement: \"FOOTER\",\n            path: \"https://anheuserbuschinbev.sharepoint.com/sites/GlobalTargetSettingandCascading?e=1%3A22b26e28a1ef4158bfd9ae5c3c6f27bd&CID=d027784b-91a9-cd3f-baa2-f51d287c0cf0&OR=Teams-HL&CT=1733340728939&clickparams=eyJBcHBOYW1lIjoiVGVhbXMtRGVza3RvcCIsIkFwcFZlcnNpb24iOiIxNDE1LzI0MTAyMDAxMzE4IiwiSGFzRmVkZXJhdGVkVXNlciI6dHJ1ZX0%3D\",\n            title: \"menu_faq\",\n            icon: \"QuestionOutlineIcon\",\n            key: \"84C3FF12-F292-472F-B1BC-F70089F1AE91\",\n            privilege: null,\n            routeComponents: null\n        },\n        {\n            placement: \"HEADER\",\n            path: \"/notifications\",\n            title: \"common_notifications\",\n            icon: \"BellIcon\",\n            key: \"F9E9AB92-FD00-43AA-8137-1AD7724944F0\",\n            privilege: null,\n            routeComponents: null\n        }\n    ];\n    const validateItemPermission = (item, permissions)=>!item.privilege || permissions.includes(item.privilege);\n    return allMenuItems.map((item)=>{\n        if (validateItemPermission(item, permissions)) {\n            return {\n                ...item,\n                routeComponents: item.routeComponents?.filter((subItem)=>validateItemPermission(subItem, permissions))\n            };\n        }\n    }).filter((item)=>item);\n};\nfunction handler(req, res) {\n    const user = req.body?.user || {};\n    const permissions = lodash_uniq__WEBPACK_IMPORTED_MODULE_0___default()(user.permissions_json) || [];\n    return res.status(200).json({\n        type: \"success\",\n        title: \"Success!\",\n        results: filterMenuItems(permissions)\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/menu-fallback.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmenu-fallback&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fapi%2Fmenu-fallback.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();