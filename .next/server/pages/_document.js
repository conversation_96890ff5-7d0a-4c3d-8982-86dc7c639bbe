"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "./src/pages/_document.tsx":
/*!*********************************!*\
  !*** ./src/pages/_document.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ghq_abi_design_system__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ghq-abi/design-system */ \"@ghq-abi/design-system\");\n/* harmony import */ var _ghq_abi_design_system__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_ghq_abi_design_system__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _shared_constants_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ~/shared/constants/i18n */ \"./src/shared/constants/i18n.ts\");\n\n\n\n\nclass Document extends (next_document__WEBPACK_IMPORTED_MODULE_1___default()) {\n    render() {\n        const basePath = \"/catchball\";\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n            lang: this.props.__NEXT_DATA__.props.pageProps.language ?? _shared_constants_i18n__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_LANGUAGE,\n            translate: \"no\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preload\",\n                            href: `${basePath}/fonts/OpenSans-ExtraBold.woff`,\n                            as: \"font\",\n                            type: \"font/woff\",\n                            crossOrigin: \"anonymous\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/_document.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preload\",\n                            href: `${basePath}/fonts/OpenSans-ExtraBold.woff2`,\n                            as: \"font\",\n                            type: \"font/woff2\",\n                            crossOrigin: \"anonymous\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/_document.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preload\",\n                            href: `${basePath}/fonts/OpenSans-Bold.woff`,\n                            as: \"font\",\n                            type: \"font/woff\",\n                            crossOrigin: \"anonymous\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/_document.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preload\",\n                            href: `${basePath}/fonts/OpenSans-Bold.woff2`,\n                            as: \"font\",\n                            type: \"font/woff2\",\n                            crossOrigin: \"anonymous\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/_document.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preload\",\n                            href: `${basePath}/fonts/OpenSans-Light.woff`,\n                            as: \"font\",\n                            type: \"font/woff\",\n                            crossOrigin: \"anonymous\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/_document.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preload\",\n                            href: `${basePath}/fonts/OpenSans-Light.woff2`,\n                            as: \"font\",\n                            type: \"font/woff2\",\n                            crossOrigin: \"anonymous\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/_document.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preload\",\n                            href: `${basePath}/fonts/OpenSans-SemiBold.woff`,\n                            as: \"font\",\n                            type: \"font/woff\",\n                            crossOrigin: \"anonymous\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/_document.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preload\",\n                            href: `${basePath}/fonts/OpenSans-SemiBold.woff2`,\n                            as: \"font\",\n                            type: \"font/woff2\",\n                            crossOrigin: \"anonymous\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/_document.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preload\",\n                            href: `${basePath}/fonts/OpenSans-Regular.woff`,\n                            as: \"font\",\n                            type: \"font/woff\",\n                            crossOrigin: \"anonymous\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/_document.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"preload\",\n                            href: `${basePath}/fonts/OpenSans-Regular.woff2`,\n                            as: \"font\",\n                            type: \"font/woff2\",\n                            crossOrigin: \"anonymous\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/_document.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"apple-touch-icon\",\n                            sizes: \"180x180\",\n                            href: `${basePath}/img/apple-touch-icon.png`\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/_document.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            type: \"image/png\",\n                            sizes: \"32x32\",\n                            href: `${basePath}/img/favicon-32x32.png`\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/_document.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            type: \"image/png\",\n                            sizes: \"16x16\",\n                            href: `${basePath}/img/favicon-16x16.png`\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/_document.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"manifest\",\n                            href: `${basePath}/assets/site.webmanifest`\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/_document.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                            id: \"stitches\",\n                            // TODO: Check if this bit is vulnerable for XSS\n                            // eslint-disable-next-line react/no-danger\n                            dangerouslySetInnerHTML: {\n                                __html: (0,_ghq_abi_design_system__WEBPACK_IMPORTED_MODULE_2__.getCssText)()\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/_document.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/_document.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    className: _ghq_abi_design_system__WEBPACK_IMPORTED_MODULE_2__.reusables.scrollbar(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                            fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/_document.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                            fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/_document.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/_document.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/_document.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_document.tsx\n");

/***/ }),

/***/ "./src/shared/constants/i18n.ts":
/*!**************************************!*\
  !*** ./src/shared/constants/i18n.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_LANGUAGE: () => (/* binding */ DEFAULT_LANGUAGE),\n/* harmony export */   REGISTERED_LANGUAGES: () => (/* binding */ REGISTERED_LANGUAGES)\n/* harmony export */ });\nconst REGISTERED_LANGUAGES = [\n    \"de-DE\",\n    \"en-US\",\n    \"es\",\n    \"fr-CA\",\n    \"fr-FR\",\n    \"it-IT\",\n    \"ja-JP\",\n    \"ko-KR\",\n    \"nl-NL\",\n    \"pt-BR\",\n    \"ru-RU\",\n    \"uk-UA\",\n    \"vi-VN\",\n    \"zh-CN\"\n];\nconst DEFAULT_LANGUAGE = \"en-US\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvc2hhcmVkL2NvbnN0YW50cy9pMThuLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU8sTUFBTUEsdUJBQXVCO0lBQ2xDO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7Q0FDRCxDQUFVO0FBRUosTUFBTUMsbUJBQW1CLFFBQVEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AZ2hxLWFiaS9jYXRjaGJhbGwtd2ViY2xpZW50Ly4vc3JjL3NoYXJlZC9jb25zdGFudHMvaTE4bi50cz85NTYzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBSRUdJU1RFUkVEX0xBTkdVQUdFUyA9IFtcbiAgJ2RlLURFJyxcbiAgJ2VuLVVTJyxcbiAgJ2VzJyxcbiAgJ2ZyLUNBJyxcbiAgJ2ZyLUZSJyxcbiAgJ2l0LUlUJyxcbiAgJ2phLUpQJyxcbiAgJ2tvLUtSJyxcbiAgJ25sLU5MJyxcbiAgJ3B0LUJSJyxcbiAgJ3J1LVJVJyxcbiAgJ3VrLVVBJyxcbiAgJ3ZpLVZOJyxcbiAgJ3poLUNOJyxcbl0gYXMgY29uc3Q7XG5cbmV4cG9ydCBjb25zdCBERUZBVUxUX0xBTkdVQUdFID0gJ2VuLVVTJztcblxuZXhwb3J0IHR5cGUgTGFuZ3VhZ2UgPSAodHlwZW9mIFJFR0lTVEVSRURfTEFOR1VBR0VTKVtudW1iZXJdO1xuIl0sIm5hbWVzIjpbIlJFR0lTVEVSRURfTEFOR1VBR0VTIiwiREVGQVVMVF9MQU5HVUFHRSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/shared/constants/i18n.ts\n");

/***/ }),

/***/ "@ghq-abi/design-system":
/*!*****************************************!*\
  !*** external "@ghq-abi/design-system" ***!
  \*****************************************/
/***/ ((module) => {

module.exports = require("@ghq-abi/design-system");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./src/pages/_document.tsx")));
module.exports = __webpack_exports__;

})();