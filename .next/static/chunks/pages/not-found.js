/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/not-found"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2Fhome%2Femilio%2Fprojects%2FGHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT%2Fsrc%2Fpages%2Fnot-found.tsx&page=%2Fnot-found!":
/*!************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2Fhome%2Femilio%2Fprojects%2FGHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT%2Fsrc%2Fpages%2Fnot-found.tsx&page=%2Fnot-found! ***!
  \************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/not-found\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/not-found.tsx */ \"./src/pages/not-found.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/not-found\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD0lMkZob21lJTJGZW1pbGlvJTJGcHJvamVjdHMlMkZHSFFfQUJJX05PUlRIU1RBUl9DQVRDSEJBTExfV0VCQ0xJRU5UJTJGc3JjJTJGcGFnZXMlMkZub3QtZm91bmQudHN4JnBhZ2U9JTJGbm90LWZvdW5kISIsIm1hcHBpbmdzIjoiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLDREQUEyQjtBQUNsRDtBQUNBO0FBQ0EsT0FBTyxJQUFVO0FBQ2pCLE1BQU0sVUFBVTtBQUNoQjtBQUNBLE9BQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/MGE0YiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL25vdC1mb3VuZFwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vc3JjL3BhZ2VzL25vdC1mb3VuZC50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL25vdC1mb3VuZFwiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2Fhome%2Femilio%2Fprojects%2FGHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT%2Fsrc%2Fpages%2Fnot-found.tsx&page=%2Fnot-found!\n"));

/***/ }),

/***/ "./src/app/templates/AuthError/index.tsx":
/*!***********************************************!*\
  !*** ./src/app/templates/AuthError/index.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthErrorTemplate: function() { return /* binding */ AuthErrorTemplate; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _public_img_abi_logo_old_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ~/../public/img/abi_logo_old.png */ \"./public/img/abi_logo_old.png\");\n/* harmony import */ var _styles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./styles */ \"./src/app/templates/AuthError/styles.ts\");\n\n\n\n\nfunction AuthErrorTemplate(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_styles__WEBPACK_IMPORTED_MODULE_3__.StyledWrapper, {\n        justify: \"center\",\n        align: \"center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_styles__WEBPACK_IMPORTED_MODULE_3__.StyledCard, {\n            direction: \"column\",\n            justify: \"center\",\n            align: \"center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    src: _public_img_abi_logo_old_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                    placeholder: \"blur\",\n                    alt: \"AB InBev\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/app/templates/AuthError/index.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/app/templates/AuthError/index.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/app/templates/AuthError/index.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n_c = AuthErrorTemplate;\nvar _c;\n$RefreshReg$(_c, \"AuthErrorTemplate\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvYXBwL3RlbXBsYXRlcy9BdXRoRXJyb3IvaW5kZXgudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBK0I7QUFFd0I7QUFFRjtBQU05QyxTQUFTSSxrQkFBa0IsS0FBb0M7UUFBcEMsRUFBRUMsUUFBUSxFQUEwQixHQUFwQztJQUNoQyxxQkFDRSw4REFBQ0Ysa0RBQWFBO1FBQUNHLFNBQVE7UUFBU0MsT0FBTTtrQkFDcEMsNEVBQUNMLCtDQUFVQTtZQUFDTSxXQUFVO1lBQVNGLFNBQVE7WUFBU0MsT0FBTTs7OEJBQ3BELDhEQUFDUCxtREFBS0E7b0JBQUNTLEtBQUtSLG9FQUFPQTtvQkFBRVMsYUFBWTtvQkFBT0MsS0FBSTs7Ozs7O2dCQUMzQ047Ozs7Ozs7Ozs7OztBQUlUO0tBVGdCRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL3RlbXBsYXRlcy9BdXRoRXJyb3IvaW5kZXgudHN4P2MwMGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEltYWdlIGZyb20gJ25leHQvaW1hZ2UnO1xuXG5pbXBvcnQgYWJpTG9nbyBmcm9tICd+Ly4uL3B1YmxpYy9pbWcvYWJpX2xvZ29fb2xkLnBuZyc7XG5cbmltcG9ydCB7IFN0eWxlZENhcmQsIFN0eWxlZFdyYXBwZXIgfSBmcm9tICcuL3N0eWxlcyc7XG5cbnR5cGUgQXV0aEVycm9yVGVtcGxhdGVQcm9wcyA9IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn07XG5cbmV4cG9ydCBmdW5jdGlvbiBBdXRoRXJyb3JUZW1wbGF0ZSh7IGNoaWxkcmVuIH06IEF1dGhFcnJvclRlbXBsYXRlUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8U3R5bGVkV3JhcHBlciBqdXN0aWZ5PVwiY2VudGVyXCIgYWxpZ249XCJjZW50ZXJcIj5cbiAgICAgIDxTdHlsZWRDYXJkIGRpcmVjdGlvbj1cImNvbHVtblwiIGp1c3RpZnk9XCJjZW50ZXJcIiBhbGlnbj1cImNlbnRlclwiPlxuICAgICAgICA8SW1hZ2Ugc3JjPXthYmlMb2dvfSBwbGFjZWhvbGRlcj1cImJsdXJcIiBhbHQ9XCJBQiBJbkJldlwiIC8+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvU3R5bGVkQ2FyZD5cbiAgICA8L1N0eWxlZFdyYXBwZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiSW1hZ2UiLCJhYmlMb2dvIiwiU3R5bGVkQ2FyZCIsIlN0eWxlZFdyYXBwZXIiLCJBdXRoRXJyb3JUZW1wbGF0ZSIsImNoaWxkcmVuIiwianVzdGlmeSIsImFsaWduIiwiZGlyZWN0aW9uIiwic3JjIiwicGxhY2Vob2xkZXIiLCJhbHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/app/templates/AuthError/index.tsx\n"));

/***/ }),

/***/ "./src/app/templates/AuthError/styles.ts":
/*!***********************************************!*\
  !*** ./src/app/templates/AuthError/styles.ts ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StyledCard: function() { return /* binding */ StyledCard; },\n/* harmony export */   StyledWrapper: function() { return /* binding */ StyledWrapper; }\n/* harmony export */ });\n/* harmony import */ var _ghq_abi_design_system__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ghq-abi/design-system */ \"./node_modules/@ghq-abi/design-system/dist/@ghq-abi/design-system.js\");\n\nconst StyledWrapper = (0,_ghq_abi_design_system__WEBPACK_IMPORTED_MODULE_0__.styled)(_ghq_abi_design_system__WEBPACK_IMPORTED_MODULE_0__.Flex, {\n    mx: \"$4\",\n    minHeight: \"inherit\"\n});\nconst StyledCard = (0,_ghq_abi_design_system__WEBPACK_IMPORTED_MODULE_0__.styled)(_ghq_abi_design_system__WEBPACK_IMPORTED_MODULE_0__.Flex, {\n    backgroundColor: \"$gray300\",\n    padding: \"$4\",\n    borderRadius: \"$2\",\n    maxWidth: \"$xl\"\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvYXBwL3RlbXBsYXRlcy9BdXRoRXJyb3Ivc3R5bGVzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFzRDtBQUUvQyxNQUFNRSxnQkFBZ0JELDhEQUFNQSxDQUFDRCx3REFBSUEsRUFBRTtJQUN4Q0csSUFBSTtJQUNKQyxXQUFXO0FBQ2IsR0FBRztBQUVJLE1BQU1DLGFBQWFKLDhEQUFNQSxDQUFDRCx3REFBSUEsRUFBRTtJQUNyQ00saUJBQWlCO0lBQ2pCQyxTQUFTO0lBQ1RDLGNBQWM7SUFDZEMsVUFBVTtBQUNaLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC90ZW1wbGF0ZXMvQXV0aEVycm9yL3N0eWxlcy50cz81ZDM2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEZsZXgsIHN0eWxlZCB9IGZyb20gJ0BnaHEtYWJpL2Rlc2lnbi1zeXN0ZW0nO1xuXG5leHBvcnQgY29uc3QgU3R5bGVkV3JhcHBlciA9IHN0eWxlZChGbGV4LCB7XG4gIG14OiAnJDQnLFxuICBtaW5IZWlnaHQ6ICdpbmhlcml0Jyxcbn0pO1xuXG5leHBvcnQgY29uc3QgU3R5bGVkQ2FyZCA9IHN0eWxlZChGbGV4LCB7XG4gIGJhY2tncm91bmRDb2xvcjogJyRncmF5MzAwJyxcbiAgcGFkZGluZzogJyQ0JyxcbiAgYm9yZGVyUmFkaXVzOiAnJDInLFxuICBtYXhXaWR0aDogJyR4bCcsXG59KTtcbiJdLCJuYW1lcyI6WyJGbGV4Iiwic3R5bGVkIiwiU3R5bGVkV3JhcHBlciIsIm14IiwibWluSGVpZ2h0IiwiU3R5bGVkQ2FyZCIsImJhY2tncm91bmRDb2xvciIsInBhZGRpbmciLCJib3JkZXJSYWRpdXMiLCJtYXhXaWR0aCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/app/templates/AuthError/styles.ts\n"));

/***/ }),

/***/ "./src/pages/not-found.tsx":
/*!*********************************!*\
  !*** ./src/pages/not-found.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NotFoundPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AiOutlineArrowUp_react_icons_ai__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AiOutlineArrowUp!=!react-icons/ai */ \"./node_modules/react-icons/ai/index.esm.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ghq_abi_design_system__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ghq-abi/design-system */ \"./node_modules/@ghq-abi/design-system/dist/@ghq-abi/design-system.js\");\n/* harmony import */ var _tolgee_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tolgee/react */ \"./node_modules/@tolgee/react/dist/tolgee-react.esm.mjs\");\n/* harmony import */ var _app_templates_AuthError__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ~/app/templates/AuthError */ \"./src/app/templates/AuthError/index.tsx\");\n/* harmony import */ var _shared_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ~/shared/components */ \"./src/shared/components/index.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction NotFoundPage() {\n    _s();\n    const { t } = (0,_tolgee_react__WEBPACK_IMPORTED_MODULE_6__.useTranslate)(\"default\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    children: \"404 | \".concat(t(\"error_pages.404.title\"))\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/not-found.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/not-found.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_templates_AuthError__WEBPACK_IMPORTED_MODULE_4__.AuthErrorTemplate, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ghq_abi_design_system__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                        css: {\n                            lineHeight: \"$shorter\",\n                            fontSize: \"$2\",\n                            mb: \"$sm\"\n                        },\n                        children: [\n                            \"404 | \",\n                            t(\"error_pages.404.message\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/not-found.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_components__WEBPACK_IMPORTED_MODULE_5__.Flags, {\n                        authorizedFlags: \"catchball\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_components__WEBPACK_IMPORTED_MODULE_5__.Can, {\n                            I: \"view\",\n                            a: \"Home\",\n                            children: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ghq_abi_design_system__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        as: \"span\",\n                                        css: {\n                                            color: \"#0053ad\",\n                                            fontWeight: \"$medium\",\n                                            fontSize: \"$xs\",\n                                            borderBottom: \"1px solid #0053ad\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AiOutlineArrowUp_react_icons_ai__WEBPACK_IMPORTED_MODULE_7__.AiOutlineArrowUp, {\n                                                style: {\n                                                    transform: \"rotate(-45deg)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/not-found.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \",\n                                            t(\"error_pages.404.go_back_to_home_page\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/not-found.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/not-found.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 15\n                                }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/not-found.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/not-found.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/GHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT/src/pages/not-found.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(NotFoundPage, \"+IvgfkiUyuIyRZTNmw/gY5SaGMg=\", false, function() {\n    return [\n        _tolgee_react__WEBPACK_IMPORTED_MODULE_6__.useTranslate\n    ];\n});\n_c = NotFoundPage;\nvar _c;\n$RefreshReg$(_c, \"NotFoundPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvbm90LWZvdW5kLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQWtEO0FBQ3JCO0FBQ0E7QUFDaUI7QUFDRDtBQUVpQjtBQUNiO0FBRWxDLFNBQVNROztJQUN0QixNQUFNLEVBQUVDLENBQUMsRUFBRSxHQUFHTCwyREFBWUEsQ0FBQztJQUUzQixxQkFDRTs7MEJBQ0UsOERBQUNILGtEQUFJQTswQkFDSCw0RUFBQ1M7OEJBQU8sU0FBb0MsT0FBM0JELEVBQUU7Ozs7Ozs7Ozs7OzBCQUdyQiw4REFBQ0osdUVBQWlCQTs7a0NBQ2hCLDhEQUFDRix3REFBSUE7d0JBQUNRLEtBQUs7NEJBQUVDLFlBQVk7NEJBQVlDLFVBQVU7NEJBQU1DLElBQUk7d0JBQU07OzRCQUFHOzRCQUN6REwsRUFBRTs7Ozs7OztrQ0FHWCw4REFBQ0YscURBQUtBO3dCQUFDUSxpQkFBZ0I7a0NBQ3JCLDRFQUFDVCxtREFBR0E7NEJBQUNVLEdBQUU7NEJBQU9DLEdBQUU7c0NBQ2Isa0JBQ0MsOERBQUNmLGtEQUFJQTtvQ0FBQ2dCLE1BQUs7OENBQ1QsNEVBQUNmLHdEQUFJQTt3Q0FDSGdCLElBQUc7d0NBQ0hSLEtBQUs7NENBQ0hTLE9BQU87NENBQ1BDLFlBQVk7NENBQ1pSLFVBQVU7NENBQ1ZTLGNBQWM7d0NBQ2hCOzswREFFQSw4REFBQ3RCLG9HQUFnQkE7Z0RBQUN1QixPQUFPO29EQUFFQyxXQUFXO2dEQUFpQjs7Ozs7OzRDQUFNOzRDQUM1RGYsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBU3JCO0dBckN3QkQ7O1FBQ1JKLHVEQUFZQTs7O0tBREpJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9wYWdlcy9ub3QtZm91bmQudHN4PzJlMzQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQWlPdXRsaW5lQXJyb3dVcCB9IGZyb20gJ3JlYWN0LWljb25zL2FpJztcbmltcG9ydCBIZWFkIGZyb20gJ25leHQvaGVhZCc7XG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuaW1wb3J0IHsgVGV4dCB9IGZyb20gJ0BnaHEtYWJpL2Rlc2lnbi1zeXN0ZW0nO1xuaW1wb3J0IHsgdXNlVHJhbnNsYXRlIH0gZnJvbSAnQHRvbGdlZS9yZWFjdCc7XG5cbmltcG9ydCB7IEF1dGhFcnJvclRlbXBsYXRlIH0gZnJvbSAnfi9hcHAvdGVtcGxhdGVzL0F1dGhFcnJvcic7XG5pbXBvcnQgeyBDYW4sIEZsYWdzIH0gZnJvbSAnfi9zaGFyZWQvY29tcG9uZW50cyc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE5vdEZvdW5kUGFnZSgpIHtcbiAgY29uc3QgeyB0IH0gPSB1c2VUcmFuc2xhdGUoJ2RlZmF1bHQnKTtcblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICA8SGVhZD5cbiAgICAgICAgPHRpdGxlPntgNDA0IHwgJHt0KCdlcnJvcl9wYWdlcy40MDQudGl0bGUnKX1gfTwvdGl0bGU+XG4gICAgICA8L0hlYWQ+XG5cbiAgICAgIDxBdXRoRXJyb3JUZW1wbGF0ZT5cbiAgICAgICAgPFRleHQgY3NzPXt7IGxpbmVIZWlnaHQ6ICckc2hvcnRlcicsIGZvbnRTaXplOiAnJDInLCBtYjogJyRzbScgfX0+XG4gICAgICAgICAgNDA0IHwge3QoJ2Vycm9yX3BhZ2VzLjQwNC5tZXNzYWdlJyl9XG4gICAgICAgIDwvVGV4dD5cblxuICAgICAgICA8RmxhZ3MgYXV0aG9yaXplZEZsYWdzPVwiY2F0Y2hiYWxsXCI+XG4gICAgICAgICAgPENhbiBJPVwidmlld1wiIGE9XCJIb21lXCI+XG4gICAgICAgICAgICB7KCkgPT4gKFxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL1wiPlxuICAgICAgICAgICAgICAgIDxUZXh0XG4gICAgICAgICAgICAgICAgICBhcz1cInNwYW5cIlxuICAgICAgICAgICAgICAgICAgY3NzPXt7XG4gICAgICAgICAgICAgICAgICAgIGNvbG9yOiAnIzAwNTNhZCcsXG4gICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICckbWVkaXVtJyxcbiAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICckeHMnLFxuICAgICAgICAgICAgICAgICAgICBib3JkZXJCb3R0b206ICcxcHggc29saWQgIzAwNTNhZCcsXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxBaU91dGxpbmVBcnJvd1VwIHN0eWxlPXt7IHRyYW5zZm9ybTogJ3JvdGF0ZSgtNDVkZWcpJyB9fSAvPnsnICd9XG4gICAgICAgICAgICAgICAgICB7dCgnZXJyb3JfcGFnZXMuNDA0LmdvX2JhY2tfdG9faG9tZV9wYWdlJyl9XG4gICAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvQ2FuPlxuICAgICAgICA8L0ZsYWdzPlxuICAgICAgPC9BdXRoRXJyb3JUZW1wbGF0ZT5cbiAgICA8Lz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJBaU91dGxpbmVBcnJvd1VwIiwiSGVhZCIsIkxpbmsiLCJUZXh0IiwidXNlVHJhbnNsYXRlIiwiQXV0aEVycm9yVGVtcGxhdGUiLCJDYW4iLCJGbGFncyIsIk5vdEZvdW5kUGFnZSIsInQiLCJ0aXRsZSIsImNzcyIsImxpbmVIZWlnaHQiLCJmb250U2l6ZSIsIm1iIiwiYXV0aG9yaXplZEZsYWdzIiwiSSIsImEiLCJocmVmIiwiYXMiLCJjb2xvciIsImZvbnRXZWlnaHQiLCJib3JkZXJCb3R0b20iLCJzdHlsZSIsInRyYW5zZm9ybSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/not-found.tsx\n"));

/***/ }),

/***/ "./node_modules/next/head.js":
/*!***********************************!*\
  !*** ./node_modules/next/head.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/shared/lib/head */ \"./node_modules/next/dist/shared/lib/head.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzIiwibWFwcGluZ3MiOiJBQUFBLGlIQUFrRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzPzg4NDkiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3Qvc2hhcmVkL2xpYi9oZWFkJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/head.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2Fhome%2Femilio%2Fprojects%2FGHQ_ABI_NORTHSTAR_CATCHBALL_WEBCLIENT%2Fsrc%2Fpages%2Fnot-found.tsx&page=%2Fnot-found!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);